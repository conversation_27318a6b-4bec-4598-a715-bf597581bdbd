
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

model_path = "./DeepSeek-TNG-R1T2-Chimera"

# Load the tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_path)

# Load the model
# Note: This model is very large and may require a powerful machine with a lot of RAM and VRAM.
# If you encounter memory issues, you may need to use a machine with more resources.
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.bfloat16,
    device_map="auto",
)

# Create a text generation pipeline
from transformers import pipeline

pipe = pipeline("text-generation", model=model, tokenizer=tokenizer)

# Generate text
prompt = "Hello, I'm a language model, and"
generated_text = pipe(prompt, max_length=50, num_return_sequences=1)

print(generated_text[0]['generated_text'])
