/* Logos Scripture Companion Design System */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Red & Black Spiritual Theme */
    --background: 0 0% 8%;
    --foreground: 0 0% 95%;

    --card: 0 0% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 95%;

    /* Red & Black Sacred Theme */
    --primary: 0 85% 55%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 0 100% 70%;

    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 90%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 75% 60%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 25%;
    --input: 0 0% 18%;
    --ring: 0 85% 55%;

    --radius: 0.75rem;

    /* Scripture-specific colors */
    --scripture-christian: 210 70% 45%;
    --scripture-islamic: 120 50% 35%;
    --scripture-hindu: 280 60% 45%;
    --scripture-buddhist: 25 70% 50%;
    --scripture-jewish: 240 60% 40%;

    /* Voice interface colors */
    --voice-active: 120 70% 50%;
    --voice-inactive: 25 15% 65%;
    --voice-listening: 45 90% 55%;

    /* Gradients */
    --gradient-sacred: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-wisdom: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));
    --gradient-voice: linear-gradient(135deg, hsl(var(--voice-active)), hsl(var(--voice-listening)));
    --gradient-orb: radial-gradient(circle, hsl(var(--primary-glow)) 0%, hsl(var(--primary)) 50%, transparent 70%);

    /* Typography */
    --font-scripture: 'Crimson Text', serif;
    --font-interface: 'Inter', sans-serif;

    /* Animations & Transitions */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-voice: all 0.2s ease-in-out;
    --transition-orb: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* Shadows */
    --shadow-sacred: 0 8px 32px -8px hsl(var(--primary) / 0.3);
    --shadow-wisdom: 0 4px 20px -4px hsl(var(--accent) / 0.2);
    --shadow-voice: 0 0 20px hsl(var(--voice-active) / 0.3);
    --shadow-orb: 0 0 40px hsl(var(--primary) / 0.6), 0 0 80px hsl(var(--primary) / 0.4), 0 0 120px hsl(var(--primary) / 0.2);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode spiritual theme */
    --background: 220 25% 8%;
    --foreground: 35 25% 95%;

    --card: 220 25% 10%;
    --card-foreground: 35 25% 95%;

    --popover: 220 25% 10%;
    --popover-foreground: 35 25% 95%;

    --primary: 45 85% 65%;
    --primary-foreground: 220 25% 8%;
    --primary-glow: 45 90% 75%;

    --secondary: 220 20% 15%;
    --secondary-foreground: 35 25% 90%;

    --muted: 220 20% 12%;
    --muted-foreground: 35 15% 65%;

    --accent: 45 80% 55%;
    --accent-foreground: 220 25% 8%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 35 25% 95%;

    --border: 220 20% 20%;
    --input: 220 20% 15%;
    --ring: 45 80% 55%;

    /* Dark mode scripture colors */
    --scripture-christian: 210 70% 65%;
    --scripture-islamic: 120 50% 55%;
    --scripture-hindu: 280 60% 65%;
    --scripture-buddhist: 25 70% 70%;
    --scripture-jewish: 240 60% 60%;

    /* Dark mode voice colors */
    --voice-active: 120 70% 60%;
    --voice-inactive: 35 15% 50%;
    --voice-listening: 45 90% 65%;

    --sidebar-background: 220 25% 8%;
    --sidebar-foreground: 35 25% 90%;
    --sidebar-primary: 45 85% 65%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 20% 15%;
    --sidebar-accent-foreground: 35 25% 90%;
    --sidebar-border: 220 20% 20%;
    --sidebar-ring: 45 80% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-interface);
  }

  /* Scripture text styling */
  .scripture-text {
    font-family: var(--font-scripture);
    line-height: 1.7;
    font-size: 1.1rem;
  }

  /* Voice interface animations */
  .voice-listening {
    animation: pulse-voice 2s infinite;
  }

  .voice-speaking {
    animation: wave-voice 1.5s infinite;
  }

  /* Glowing Orb Animations */
  .orb-idle {
    animation: orb-breathe 4s ease-in-out infinite;
  }

  .orb-speaking {
    animation: orb-gentle-pulse 3s ease-in-out infinite, orb-color-shift 8s ease-in-out infinite;
  }

  .orb-listening {
    animation: orb-listening-pulse 2.5s ease-in-out infinite;
  }

  .orb-paused {
    animation: orb-paused-glow 2s ease-in-out infinite;
  }

  @keyframes pulse-voice {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 hsl(var(--voice-active) / 0.7);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px hsl(var(--voice-active) / 0);
    }
  }

  @keyframes wave-voice {
    0%, 100% { transform: scale(1); }
    33% { transform: scale(1.1); }
    66% { transform: scale(0.9); }
  }

  @keyframes orb-breathe {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 40px hsl(0 85% 55% / 0.4), 0 0 80px hsl(0 85% 55% / 0.3), 0 0 120px hsl(0 85% 55% / 0.2);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 50px hsl(0 85% 55% / 0.6), 0 0 100px hsl(0 85% 55% / 0.4), 0 0 150px hsl(0 85% 55% / 0.3);
    }
  }

  @keyframes orb-gentle-pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.08);
    }
  }

  @keyframes orb-color-shift {
    0% {
      filter: hue-rotate(0deg) brightness(1);
      box-shadow: 0 0 40px hsl(0 85% 55% / 0.5), 0 0 80px hsl(0 85% 55% / 0.3), 0 0 120px hsl(0 85% 55% / 0.2);
    }
    20% {
      filter: hue-rotate(30deg) brightness(1.05);
      box-shadow: 0 0 40px hsl(30 85% 55% / 0.5), 0 0 80px hsl(30 85% 55% / 0.3), 0 0 120px hsl(30 85% 55% / 0.2);
    }
    40% {
      filter: hue-rotate(60deg) brightness(1.1);
      box-shadow: 0 0 40px hsl(60 85% 55% / 0.5), 0 0 80px hsl(60 85% 55% / 0.3), 0 0 120px hsl(60 85% 55% / 0.2);
    }
    60% {
      filter: hue-rotate(240deg) brightness(1.05);
      box-shadow: 0 0 40px hsl(240 85% 55% / 0.5), 0 0 80px hsl(240 85% 55% / 0.3), 0 0 120px hsl(240 85% 55% / 0.2);
    }
    80% {
      filter: hue-rotate(300deg) brightness(1.08);
      box-shadow: 0 0 40px hsl(300 85% 55% / 0.5), 0 0 80px hsl(300 85% 55% / 0.3), 0 0 120px hsl(300 85% 55% / 0.2);
    }
    100% {
      filter: hue-rotate(360deg) brightness(1);
      box-shadow: 0 0 40px hsl(0 85% 55% / 0.5), 0 0 80px hsl(0 85% 55% / 0.3), 0 0 120px hsl(0 85% 55% / 0.2);
    }
  }

  @keyframes orb-listening-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 30px hsl(240 85% 55% / 0.4), 0 0 60px hsl(240 85% 55% / 0.3), 0 0 90px hsl(240 85% 55% / 0.2);
    }
    50% {
      transform: scale(1.12);
      box-shadow: 0 0 40px hsl(240 85% 55% / 0.6), 0 0 80px hsl(240 85% 55% / 0.4), 0 0 120px hsl(240 85% 55% / 0.3);
    }
  }

  @keyframes orb-paused-glow {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 25px hsl(45 85% 55% / 0.4), 0 0 50px hsl(45 85% 55% / 0.3), 0 0 75px hsl(45 85% 55% / 0.2);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 35px hsl(45 85% 55% / 0.6), 0 0 70px hsl(45 85% 55% / 0.4), 0 0 105px hsl(45 85% 55% / 0.3);
    }
  }

  /* Smooth transitions for all interactive elements */
  button, input, [role="button"] {
    transition: var(--transition-smooth);
  }

  /* Custom scrollbar for chat */
  .chat-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .chat-scroll::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .chat-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }

  .chat-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent) / 0.7);
  }
}