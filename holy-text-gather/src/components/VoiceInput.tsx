import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Mic } from 'lucide-react';

interface VoiceInputProps {
  onTranscript: (transcript: string) => void;
  onError: (error: string) => void;
  isListening: boolean;
  setIsListening: (listening: boolean) => void;
}

const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  onError,
  isListening,
  setIsListening,
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = 
      (window as any).SpeechRecognition || 
      (window as any).webkitSpeechRecognition;

    if (SpeechRecognition) {
      setIsSupported(true);
      recognitionRef.current = new SpeechRecognition();
      
      const recognition = recognitionRef.current;
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        setIsListening(true);
      };

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        onTranscript(transcript);
        setIsListening(false);
      };

      recognition.onerror = (event: any) => {
        const errorMessage = getErrorMessage(event.error);
        onError(errorMessage);
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };
    } else {
      setIsSupported(false);
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onTranscript, onError, setIsListening]);

  const getErrorMessage = (error: string): string => {
    switch (error) {
      case 'no-speech':
        return 'No speech was detected. Please try again.';
      case 'audio-capture':
        return 'No microphone was found. Please check your microphone settings.';
      case 'not-allowed':
        return 'Microphone permission denied. Please allow microphone access.';
      case 'network':
        return 'Network error occurred. Please check your connection.';
      default:
        return 'An error occurred with speech recognition. Please try again.';
    }
  };

  const handleMicClick = () => {
    if (!isSupported) {
      onError('Speech recognition is not supported in your browser.');
      return;
    }

    if (isListening) {
      recognitionRef.current?.stop();
      setIsListening(false);
    } else {
      try {
        recognitionRef.current?.start();
      } catch (error) {
        onError('Failed to start speech recognition. Please try again.');
      }
    }
  };

  if (!isSupported) {
    return null;
  }

  return (
    <Button
      onClick={handleMicClick}
      variant="outline"
      size="icon"
      className={`
        relative transition-all duration-300
        ${isListening 
          ? 'bg-voice-active text-white shadow-voice voice-listening' 
          : 'bg-card hover:bg-voice-active/10 hover:border-voice-active'
        }
      `}
      disabled={false}
    >
      <Mic className={`h-4 w-4 ${isListening ? 'animate-pulse' : ''}`} />
      
      {isListening && (
        <div className="absolute inset-0 rounded-md bg-voice-active/20 animate-pulse" />
      )}
    </Button>
  );
};

export default VoiceInput;