import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ScripturePassage {
  faith: string;
  book: string;
  chapter: number;
  verse: string;
  text: string;
  citation: string;
}

interface ScriptureCardProps {
  scripture: ScripturePassage;
}

const ScriptureCard: React.FC<ScriptureCardProps> = ({ scripture }) => {
  const getFaithColor = (faith: string) => {
    const faithLower = faith.toLowerCase();
    switch (faithLower) {
      case 'christianity':
      case 'christian':
        return 'scripture-christian';
      case 'islam':
      case 'islamic':
        return 'scripture-islamic';
      case 'hinduism':
      case 'hindu':
        return 'scripture-hindu';
      case 'buddhism':
      case 'buddhist':
        return 'scripture-buddhist';
      case 'judaism':
      case 'jewish':
        return 'scripture-jewish';
      default:
        return 'accent';
    }
  };

  const getFaithSymbol = (faith: string) => {
    const faithLower = faith.toLowerCase();
    switch (faithLower) {
      case 'christianity':
      case 'christian':
        return '✟';
      case 'islam':
      case 'islamic':
        return '☪';
      case 'hinduism':
      case 'hindu':
        return 'ॐ';
      case 'buddhism':
      case 'buddhist':
        return '☸';
      case 'judaism':
      case 'jewish':
        return '✡';
      default:
        return '◦';
    }
  };

  return (
    <Card className="p-4 border-l-4 shadow-wisdom bg-card/50 backdrop-blur-sm" 
          style={{ borderLeftColor: `hsl(var(--${getFaithColor(scripture.faith)}))` }}>
      <div className="space-y-3">
        {/* Faith and Citation Header */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            <span className="text-lg" style={{ color: `hsl(var(--${getFaithColor(scripture.faith)}))` }}>
              {getFaithSymbol(scripture.faith)}
            </span>
            <Badge 
              variant="secondary" 
              className="text-xs"
              style={{ 
                backgroundColor: `hsl(var(--${getFaithColor(scripture.faith)}) / 0.1)`,
                color: `hsl(var(--${getFaithColor(scripture.faith)}))`
              }}
            >
              {scripture.faith}
            </Badge>
          </div>
          <div className="text-sm text-muted-foreground font-interface">
            {scripture.book} {scripture.chapter}:{scripture.verse}
          </div>
        </div>

        {/* Scripture Text */}
        <blockquote className="scripture-text text-foreground leading-relaxed border-none p-0 italic">
          "{scripture.text}"
        </blockquote>

        {/* Citation */}
        <div className="text-xs text-muted-foreground font-interface">
          [{scripture.citation}]
        </div>
      </div>
    </Card>
  );
};

export default ScriptureCard;