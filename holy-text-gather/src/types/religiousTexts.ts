// Enhanced types for multi-religious text support

export interface BaseScripture {
  id: string;
  faith: ReligiousFaith;
  text: string;
  originalText?: string; // Original language text
  transliteration?: string; // Romanized version
  translation?: string; // Translation info
  translator?: string;
  source?: string;
  context?: string;
  tags?: string[];
  audioUrl?: string;
  metadata?: Record<string, any>;
}

export interface ChristianScripture extends BaseScripture {
  faith: 'Christianity';
  book: string; // <PERSON>, Matthew, etc.
  chapter: number;
  verse: number | string; // Can be "1-3" for ranges
  version: string; // KJV, NIV, ESV, etc.
  testament: 'Old' | 'New';
}

export interface IslamicScripture extends BaseScripture {
  faith: 'Islam';
  type: 'Quran' | 'Hadith';
  // For Quran
  surah?: number;
  ayah?: number | string;
  surahName?: string;
  surahNameArabic?: string;
  juz?: number;
  hizb?: number;
  ruku?: number;
  page?: number;
  // For Hadith
  collection?: string; // <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc.
  book?: string;
  hadithNumber?: number;
  narrator?: string;
  grade?: string; // <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>'if, etc.
}

export interface JewishScripture extends BaseScripture {
  faith: 'Judaism';
  type: 'Torah' | 'Tanakh' | 'Talmud' | 'Mishnah' | 'Other';
  book?: string; // For Torah/Tanakh
  chapter?: number;
  verse?: number | string;
  // For Talmud
  tractate?: string;
  page?: string; // Talmud page format (e.g., "2a", "15b")
  // For Mishnah
  order?: string;
  tractateNumber?: number;
  mishnahNumber?: number;
}

export interface HinduScripture extends BaseScripture {
  faith: 'Hinduism';
  type: 'Vedas' | 'Upanishads' | 'Puranas' | 'Epics' | 'Other';
  // For Vedas
  veda?: 'Rig' | 'Sama' | 'Yajur' | 'Atharva';
  mandala?: number; // For Rig Veda
  sukta?: number;
  rik?: number;
  // For Upanishads
  upanishad?: string;
  // For Epics (Ramayana, Mahabharata)
  epic?: 'Ramayana' | 'Mahabharata';
  kanda?: number; // For Ramayana
  parva?: number; // For Mahabharata
  adhyaya?: number; // Chapter
  shloka?: number; // Verse
  // For Bhagavad Gita (part of Mahabharata)
  chapter?: number;
  verse?: number;
}

export interface BuddhistScripture extends BaseScripture {
  faith: 'Buddhism';
  type: 'Tripitaka' | 'Mahayana' | 'Vajrayana' | 'Other';
  tradition: 'Theravada' | 'Mahayana' | 'Vajrayana';
  // For Tripitaka (Pali Canon)
  pitaka?: 'Vinaya' | 'Sutta' | 'Abhidhamma';
  nikaya?: string; // Digha, Majjhima, Samyutta, Anguttara, Khuddaka
  sutta?: string;
  section?: number;
  verse?: number;
  // For other texts
  textName?: string; // Dhammapada, Lotus Sutra, etc.
  chapter?: number;
}

export interface SikhScripture extends BaseScripture {
  faith: 'Sikhism';
  type: 'Guru Granth Sahib' | 'Dasam Granth' | 'Other';
  guru?: string; // Which Guru or saint
  raag?: string; // Musical mode
  page?: number; // Page in Guru Granth Sahib
  ang?: number; // Alternative term for page
  shabad?: number; // Hymn number
  pankti?: number; // Line number
}

export interface OtherReligiousScripture extends BaseScripture {
  faith: 'Bahai' | 'Zoroastrianism' | 'Jainism' | 'Taoism' | 'Confucianism' | 'Other';
  book?: string;
  chapter?: number | string;
  verse?: number | string;
  section?: string;
  customFields?: Record<string, any>;
}

export type ReligiousFaith = 
  | 'Christianity' 
  | 'Islam' 
  | 'Judaism' 
  | 'Hinduism' 
  | 'Buddhism' 
  | 'Sikhism' 
  | 'Bahai' 
  | 'Zoroastrianism' 
  | 'Jainism' 
  | 'Taoism' 
  | 'Confucianism' 
  | 'Other';

export type AnyScripture = 
  | ChristianScripture 
  | IslamicScripture 
  | JewishScripture 
  | HinduScripture 
  | BuddhistScripture 
  | SikhScripture 
  | OtherReligiousScripture;

// Search and query interfaces
export interface ScriptureQuery {
  faith?: ReligiousFaith | ReligiousFaith[];
  keyword?: string;
  reference?: string; // "Matthew 5:16", "Quran 2:255", etc.
  tags?: string[];
  limit?: number;
  offset?: number;
}

export interface ScriptureSearchResult {
  scriptures: AnyScripture[];
  total: number;
  query: ScriptureQuery;
  suggestions?: string[];
}

// API response interfaces
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Religious text source configuration
export interface TextSource {
  name: string;
  faith: ReligiousFaith;
  apiUrl?: string;
  apiKey?: string;
  format: 'json' | 'xml' | 'text';
  rateLimit?: number;
  attribution: string;
  license?: string;
}

// Citation formatting
export interface CitationFormat {
  faith: ReligiousFaith;
  format: (scripture: AnyScripture) => string;
  shortFormat: (scripture: AnyScripture) => string;
}

// Multi-language support
export interface LanguageSupport {
  code: string; // ISO 639-1 code
  name: string;
  nativeName: string;
  rtl: boolean; // Right-to-left script
  fonts?: string[]; // Recommended fonts
}

export interface TranslationInfo {
  language: LanguageSupport;
  translator: string;
  year?: number;
  notes?: string;
  quality?: 'literal' | 'dynamic' | 'paraphrase';
}
