import { 
  ChristianScripture, 
  IslamicScripture, 
  APIResponse,
  AnyScripture 
} from '../types/religiousTexts';

// API Configuration
const API_CONFIGS = {
  bible: {
    baseUrl: 'https://api.scripture.api.bible/v1',
    // Note: In production, this should be in environment variables
    apiKey: process.env.REACT_APP_BIBLE_API_KEY || '',
    defaultVersion: 'de4e12af7f28f599-02' // NIV
  },
  quran: {
    baseUrl: 'https://api.alquran.cloud/v1',
    defaultEdition: 'en.sahih' // Sahih International
  }
};

export class ReligiousTextAPIService {
  
  // Bible API Integration
  async fetchBibleVerse(book: string, chapter: number, verse: number, version?: string): Promise<APIResponse<ChristianScripture>> {
    try {
      const versionId = version || API_CONFIGS.bible.defaultVersion;
      const reference = `${book}.${chapter}.${verse}`;
      
      const response = await fetch(
        `${API_CONFIGS.bible.baseUrl}/bibles/${versionId}/passages/${reference}`,
        {
          headers: {
            'api-key': API_CONFIGS.bible.apiKey
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Bible API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data) {
        const passage = data.data;
        const scripture: ChristianScripture = {
          id: `${book.toLowerCase()}_${chapter}_${verse}`,
          faith: 'Christianity',
          book: book,
          chapter: chapter,
          verse: verse,
          version: versionId,
          testament: this.getTestament(book),
          text: this.cleanBibleText(passage.content),
          translation: 'New International Version', // This should be dynamic based on version
          source: 'API.Bible',
          tags: this.generateBibleTags(book, passage.content)
        };

        return { success: true, data: scripture };
      }

      return { success: false, error: 'No data found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async fetchBibleChapter(book: string, chapter: number, version?: string): Promise<APIResponse<ChristianScripture[]>> {
    try {
      const versionId = version || API_CONFIGS.bible.defaultVersion;
      const reference = `${book}.${chapter}`;
      
      const response = await fetch(
        `${API_CONFIGS.bible.baseUrl}/bibles/${versionId}/chapters/${reference}`,
        {
          headers: {
            'api-key': API_CONFIGS.bible.apiKey
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Bible API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data) {
        // Parse the chapter content into individual verses
        const verses = this.parseBibleChapter(data.data.content, book, chapter, versionId);
        return { success: true, data: verses };
      }

      return { success: false, error: 'No data found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Quran API Integration
  async fetchQuranVerse(surah: number, ayah: number, edition?: string): Promise<APIResponse<IslamicScripture>> {
    try {
      const editionId = edition || API_CONFIGS.quran.defaultEdition;
      
      const response = await fetch(
        `${API_CONFIGS.quran.baseUrl}/ayah/${surah}:${ayah}/editions/quran-uthmani,${editionId}`
      );

      if (!response.ok) {
        throw new Error(`Quran API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data && data.data.length >= 2) {
        const arabicText = data.data[0];
        const translation = data.data[1];
        
        const scripture: IslamicScripture = {
          id: `quran_${surah}_${ayah}`,
          faith: 'Islam',
          type: 'Quran',
          surah: surah,
          ayah: ayah,
          surahName: translation.surah.englishName,
          surahNameArabic: translation.surah.name,
          text: translation.text,
          originalText: arabicText.text,
          translation: translation.edition.name,
          source: 'AlQuran.cloud',
          tags: this.generateQuranTags(surah, translation.text)
        };

        return { success: true, data: scripture };
      }

      return { success: false, error: 'No data found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async fetchQuranSurah(surah: number, edition?: string): Promise<APIResponse<IslamicScripture[]>> {
    try {
      const editionId = edition || API_CONFIGS.quran.defaultEdition;
      
      const response = await fetch(
        `${API_CONFIGS.quran.baseUrl}/surah/${surah}/editions/quran-uthmani,${editionId}`
      );

      if (!response.ok) {
        throw new Error(`Quran API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data && data.data.length >= 2) {
        const arabicSurah = data.data[0];
        const translationSurah = data.data[1];
        
        const verses: IslamicScripture[] = translationSurah.ayahs.map((ayah: any, index: number) => ({
          id: `quran_${surah}_${ayah.number}`,
          faith: 'Islam' as const,
          type: 'Quran' as const,
          surah: surah,
          ayah: ayah.numberInSurah,
          surahName: translationSurah.englishName,
          surahNameArabic: translationSurah.name,
          text: ayah.text,
          originalText: arabicSurah.ayahs[index]?.text || '',
          translation: translationSurah.edition.name,
          source: 'AlQuran.cloud',
          tags: this.generateQuranTags(surah, ayah.text)
        }));

        return { success: true, data: verses };
      }

      return { success: false, error: 'No data found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Search functionality
  async searchBible(query: string, version?: string): Promise<APIResponse<ChristianScripture[]>> {
    try {
      const versionId = version || API_CONFIGS.bible.defaultVersion;
      
      const response = await fetch(
        `${API_CONFIGS.bible.baseUrl}/bibles/${versionId}/search?query=${encodeURIComponent(query)}`,
        {
          headers: {
            'api-key': API_CONFIGS.bible.apiKey
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Bible API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data && data.data.verses) {
        const scriptures: ChristianScripture[] = data.data.verses.map((verse: any) => ({
          id: verse.id,
          faith: 'Christianity' as const,
          book: verse.bookId,
          chapter: parseInt(verse.chapterId.split('.')[1]),
          verse: parseInt(verse.id.split('.')[2]),
          version: versionId,
          testament: this.getTestament(verse.bookId),
          text: this.cleanBibleText(verse.text),
          translation: 'New International Version',
          source: 'API.Bible',
          tags: this.generateBibleTags(verse.bookId, verse.text)
        }));

        return { success: true, data: scriptures };
      }

      return { success: false, error: 'No results found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async searchQuran(query: string, edition?: string): Promise<APIResponse<IslamicScripture[]>> {
    try {
      const editionId = edition || API_CONFIGS.quran.defaultEdition;
      
      const response = await fetch(
        `${API_CONFIGS.quran.baseUrl}/search/${encodeURIComponent(query)}/all/${editionId}`
      );

      if (!response.ok) {
        throw new Error(`Quran API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.data && data.data.matches) {
        const scriptures: IslamicScripture[] = data.data.matches.map((match: any) => ({
          id: `quran_${match.surah.number}_${match.numberInSurah}`,
          faith: 'Islam' as const,
          type: 'Quran' as const,
          surah: match.surah.number,
          ayah: match.numberInSurah,
          surahName: match.surah.englishName,
          surahNameArabic: match.surah.name,
          text: match.text,
          translation: editionId,
          source: 'AlQuran.cloud',
          tags: this.generateQuranTags(match.surah.number, match.text)
        }));

        return { success: true, data: scriptures };
      }

      return { success: false, error: 'No results found' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Helper methods
  private getTestament(book: string): 'Old' | 'New' {
    const newTestamentBooks = [
      'MAT', 'MRK', 'LUK', 'JHN', 'ACT', 'ROM', '1CO', '2CO', 'GAL', 'EPH', 
      'PHP', 'COL', '1TH', '2TH', '1TI', '2TI', 'TIT', 'PHM', 'HEB', 'JAS', 
      '1PE', '2PE', '1JN', '2JN', '3JN', 'JUD', 'REV'
    ];
    return newTestamentBooks.includes(book.toUpperCase()) ? 'New' : 'Old';
  }

  private cleanBibleText(text: string): string {
    // Remove HTML tags and clean up formatting
    return text
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private parseBibleChapter(content: string, book: string, chapter: number, version: string): ChristianScripture[] {
    // This is a simplified parser - in production, you'd want more robust parsing
    const verses: ChristianScripture[] = [];
    const versePattern = /(\d+)\s+([^0-9]+?)(?=\d+|$)/g;
    let match;

    while ((match = versePattern.exec(content)) !== null) {
      const verseNumber = parseInt(match[1]);
      const verseText = this.cleanBibleText(match[2]);

      verses.push({
        id: `${book.toLowerCase()}_${chapter}_${verseNumber}`,
        faith: 'Christianity',
        book: book,
        chapter: chapter,
        verse: verseNumber,
        version: version,
        testament: this.getTestament(book),
        text: verseText,
        translation: 'New International Version',
        source: 'API.Bible',
        tags: this.generateBibleTags(book, verseText)
      });
    }

    return verses;
  }

  private generateBibleTags(book: string, text: string): string[] {
    const tags: string[] = [];
    
    // Add book-based tags
    if (book.toLowerCase().includes('psalm')) tags.push('psalms', 'worship', 'prayer');
    if (['matthew', 'mark', 'luke', 'john'].includes(book.toLowerCase())) tags.push('gospel', 'jesus');
    if (book.toLowerCase().includes('proverb')) tags.push('wisdom', 'guidance');
    
    // Add content-based tags
    const lowerText = text.toLowerCase();
    if (lowerText.includes('love')) tags.push('love');
    if (lowerText.includes('faith')) tags.push('faith');
    if (lowerText.includes('hope')) tags.push('hope');
    if (lowerText.includes('peace')) tags.push('peace');
    if (lowerText.includes('prayer') || lowerText.includes('pray')) tags.push('prayer');
    
    return tags;
  }

  private generateQuranTags(surah: number, text: string): string[] {
    const tags: string[] = [];
    
    // Add surah-based tags
    if (surah === 1) tags.push('al-fatihah', 'opening');
    if (surah === 2) tags.push('al-baqarah', 'cow');
    if (surah === 112) tags.push('al-ikhlas', 'sincerity');
    
    // Add content-based tags
    const lowerText = text.toLowerCase();
    if (lowerText.includes('allah')) tags.push('allah');
    if (lowerText.includes('mercy') || lowerText.includes('merciful')) tags.push('mercy');
    if (lowerText.includes('guidance')) tags.push('guidance');
    if (lowerText.includes('prayer')) tags.push('prayer');
    if (lowerText.includes('faith') || lowerText.includes('believe')) tags.push('faith');
    
    return tags;
  }
}

// Singleton instance
export const religiousTextAPI = new ReligiousTextAPIService();
